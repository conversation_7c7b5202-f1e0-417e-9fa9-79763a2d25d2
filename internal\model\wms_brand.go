package model

import "gorm.io/gorm"

type WmsBrand struct {
	gorm.Model
	Name     string `gorm:"size:64; not null; index; comment:名称"`
	Summary  string `gorm:"type:text; comment:描述"`
	Order    int    `gorm:"default:0; index; comment:排序"`
	Status   bool   `gorm:"default:false; index; comment:状态"`
	TenantId uint   `gorm:"default:0; index; comment:租户ID"`
}

func (m *WmsBrand) TableName() string {
	return "wms_brand"
}
