package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type WmsSku struct {
	gorm.Model
	Name     string         `gorm:"size:255; not null; index; comment:名称"`
	Code     string         `gorm:"size:64; not null; index; comment:编号"`
	Summary  string         `gorm:"type:text; comment:备注"`
	Cover    string         `gorm:"size:255; comment:封面"`
	From     string         `gorm:"size:64; not null; comment:来源"`
	Tags     datatypes.JSON `gorm:"type:jsonb; comment:标签"`
	Order    int            `gorm:"default:0; index; comment:排序"`
	Status   bool           `gorm:"default:false; index; comment:状态"`
	MetaId   uint           `gorm:"default:0; index; comment:分类ID"`
	UnitId   uint           `gorm:"default:0; index; comment:单位ID"`
	BrandId  uint           `gorm:"default:0; index; comment:品牌ID"`
	TenantId uint           `gorm:"default:0; index; comment:租户ID"`
}

func (m *WmsSku) TableName() string {
	return "wms_sku"
}
