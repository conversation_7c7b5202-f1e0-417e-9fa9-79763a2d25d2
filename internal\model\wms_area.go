package model

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

type WmsArea struct {
	gorm.Model
	Name      string        `gorm:"size:64; not null; index; comment:名称"`
	Order     int           `gorm:"default:0; index; comment:排序"`
	Status    bool          `gorm:"default:false; index; comment:状态"`
	ParentId  uint          `gorm:"default:0; index; comment:父级ID"`
	ParentIds pq.Int64Array `gorm:"type:integer[]; index; comment:父级IDs"`
	TenantId  uint          `gorm:"default:0; index; comment:租户ID"`
}

func (m *WmsArea) TableName() string {
	return "wms_area"
}
